<template>
  <div class="category">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">分类：{{ categoryName }}</h1>
        <p class="page-subtitle">共 {{ filteredArticles.length }} 篇文章</p>
      </div>

      <div class="content-wrapper">
        <main class="main-content">
          <!-- 文章列表 -->
          <div class="articles" v-if="paginatedArticles.length > 0">
            <article 
              v-for="article in paginatedArticles" 
              :key="article.id" 
              class="article-item"
            >
              <div class="article-image">
                <img :src="article.image" :alt="article.title" />
              </div>
              <div class="article-content">
                <div class="article-meta">
                  <span class="article-date">{{ formatDate(article.createdAt) }}</span>
                  <span class="article-views">{{ article.views }} 阅读</span>
                </div>
                <h2 class="article-title">
                  <router-link :to="`/article/${article.id}`">
                    {{ article.title }}
                  </router-link>
                </h2>
                <p class="article-excerpt">{{ article.excerpt }}</p>
                <div class="article-tags">
                  <router-link 
                    v-for="tag in article.tags" 
                    :key="tag"
                    :to="`/tag/${tag}`"
                    class="tag"
                  >
                    {{ tag }}
                  </router-link>
                </div>
                <div class="article-footer">
                  <router-link :to="`/article/${article.id}`" class="read-more">
                    阅读全文 →
                  </router-link>
                </div>
              </div>
            </article>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">📂</div>
            <h3>该分类下暂无文章</h3>
            <p>这个分类还没有发布任何文章</p>
            <router-link to="/articles" class="back-link">查看所有文章</router-link>
          </div>

          <!-- 分页 -->
          <div class="pagination" v-if="totalPages > 1">
            <button 
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="pagination-btn"
            >
              上一页
            </button>
            
            <div class="pagination-numbers">
              <button 
                v-for="page in visiblePages" 
                :key="page"
                @click="goToPage(page)"
                :class="['pagination-number', { active: page === currentPage }]"
              >
                {{ page }}
              </button>
            </div>
            
            <button 
              @click="goToPage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="pagination-btn"
            >
              下一页
            </button>
          </div>
        </main>

        <aside class="sidebar-wrapper">
          <Sidebar />
        </aside>
      </div>
    </div>
  </div>
</template>

<script>
import Sidebar from '../components/Sidebar.vue'

export default {
  name: 'Category',
  components: {
    Sidebar
  },
  data() {
    return {
      articles: [
        {
          id: 1,
          title: 'Vue 3 Composition API 深入解析',
          excerpt: '深入了解Vue 3的Composition API，掌握现代Vue开发的核心概念和最佳实践。',
          image: '/api/placeholder/200/150',
          category: 'Vue.js',
          tags: ['Vue3', 'Composition API', '前端'],
          createdAt: '2024-01-15',
          views: 1250
        },
        {
          id: 4,
          title: 'React vs Vue：2024年前端框架对比',
          excerpt: '深入对比两大主流前端框架的优缺点，帮助你选择最适合项目的技术栈。',
          image: '/api/placeholder/200/150',
          category: 'Vue.js',
          tags: ['React', 'Vue', '框架对比'],
          createdAt: '2023-12-20',
          views: 2340
        },
        {
          id: 2,
          title: 'JavaScript ES2024 新特性详解',
          excerpt: '探索JavaScript最新版本的新特性，包括新的语法糖和API改进。',
          image: '/api/placeholder/200/150',
          category: 'JavaScript',
          tags: ['JavaScript', 'ES2024', '新特性'],
          createdAt: '2024-01-10',
          views: 980
        },
        {
          id: 5,
          title: 'TypeScript 实战技巧分享',
          excerpt: '分享在实际项目中使用TypeScript的经验和技巧，提高代码质量和开发效率。',
          image: '/api/placeholder/200/150',
          category: 'JavaScript',
          tags: ['TypeScript', '实战', '技巧'],
          createdAt: '2023-12-15',
          views: 1890
        },
        {
          id: 3,
          title: 'CSS Grid 布局完全指南',
          excerpt: '从基础到高级，全面掌握CSS Grid布局系统，构建复杂的网页布局。',
          image: '/api/placeholder/200/150',
          category: 'CSS',
          tags: ['CSS', 'Grid', '布局'],
          createdAt: '2024-01-05',
          views: 756
        }
      ],
      currentPage: 1,
      pageSize: 5
    }
  },
  computed: {
    categoryName() {
      return this.$route.params.name
    },
    filteredArticles() {
      return this.articles.filter(article => article.category === this.categoryName)
    },
    totalPages() {
      return Math.ceil(this.filteredArticles.length / this.pageSize)
    },
    paginatedArticles() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredArticles.slice(start, end)
    },
    visiblePages() {
      const pages = []
      const start = Math.max(1, this.currentPage - 2)
      const end = Math.min(this.totalPages, this.currentPage + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      return pages
    }
  },
  watch: {
    '$route.params.name'() {
      this.currentPage = 1
    }
  },
  methods: {
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }
    },
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-subtitle {
  color: #666;
  font-size: 1.1rem;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.article-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  display: flex;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.article-image {
  width: 200px;
  height: 150px;
  flex-shrink: 0;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #999;
}

.article-title {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  line-height: 1.4;
}

.article-title a {
  color: #2c3e50;
  text-decoration: none;
  transition: color 0.3s ease;
}

.article-title a:hover {
  color: #3498db;
}

.article-excerpt {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
  flex: 1;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.tag {
  background: #f8f9fa;
  color: #666;
  padding: 2px 8px;
  border-radius: 12px;
  text-decoration: none;
  font-size: 0.8rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.tag:hover {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.article-footer {
  display: flex;
  justify-content: flex-end;
}

.read-more {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: #2980b9;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.empty-state p {
  color: #666;
  margin-bottom: 20px;
}

.back-link {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.back-link:hover {
  background: #2980b9;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;
}

.pagination-btn,
.pagination-number {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  text-decoration: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled),
.pagination-number:hover {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-number.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.pagination-numbers {
  display: flex;
  gap: 5px;
}

@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .article-item {
    flex-direction: column;
  }
  
  .article-image {
    width: 100%;
    height: 200px;
  }
}
</style>
