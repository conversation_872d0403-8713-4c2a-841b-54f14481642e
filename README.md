# Vue3 博客前台系统

一个基于 Vue 3 + Vite + JavaScript 构建的现代化博客前台系统，具有完整的文章展示、分类筛选、标签系统、搜索功能等特性。

## 🚀 特性

- ✨ **现代化技术栈**: Vue 3 + Vite + Vue Router
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🎨 **精美UI**: 简洁现代的界面设计
- 🔍 **搜索功能**: 支持文章标题、内容、标签搜索
- 🏷️ **标签系统**: 完整的文章分类和标签管理
- 📄 **分页功能**: 高效的文章列表分页展示
- 💬 **评论系统**: 文章评论功能（模拟数据）
- 🔗 **SEO优化**: 完整的SEO meta标签和结构化数据
- ⚡ **性能优化**: 代码分割、懒加载等优化策略

## 📦 项目结构

```
src/
├── api/           # API 接口
├── assets/        # 静态资源
│   └── css/       # 样式文件
├── components/    # 公共组件
├── data/          # 模拟数据
├── router/        # 路由配置
├── utils/         # 工具函数
├── views/         # 页面组件
├── App.vue        # 根组件
└── main.js        # 入口文件
```

## 🛠️ 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

项目将在 `http://localhost:3000` 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📄 页面说明

### 主要页面

- **首页** (`/`): 展示最新文章、热门文章等
- **文章列表** (`/articles`): 文章列表页，支持搜索、分类筛选、排序
- **文章详情** (`/article/:id`): 文章详情页，包含评论功能
- **分类页面** (`/category/:name`): 按分类展示文章
- **标签页面** (`/tag/:name`): 按标签展示文章
- **关于页面** (`/about`): 个人介绍和联系方式

### 组件说明

- **Header**: 顶部导航栏，包含搜索功能
- **Footer**: 底部信息栏
- **Sidebar**: 侧边栏，展示个人信息、最新文章、分类、标签等

## 🎨 功能特性

### 文章系统

- 文章列表展示
- 文章详情页面
- 文章分类和标签
- 文章搜索功能
- 阅读量统计

### 用户交互

- 响应式导航菜单
- 搜索功能
- 分页导航
- 评论系统（模拟）

### SEO优化

- 动态页面标题
- Meta描述和关键词
- Open Graph标签
- Twitter Card标签
- 结构化数据（JSON-LD）

## 🔧 技术栈

### 核心技术

- **Vue 3**: 渐进式JavaScript框架
- **Vite**: 下一代前端构建工具
- **Vue Router**: Vue.js官方路由管理器

### 开发工具

- **JavaScript**: ES6+ 语法
- **CSS3**: 现代CSS特性
- **HTML5**: 语义化标签

## 📱 响应式设计

项目采用移动优先的响应式设计策略：

- **桌面端**: >= 1200px
- **平板端**: 768px - 1199px
- **手机端**: < 768px

## 🎯 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 📝 开发说明

### 添加新文章

在 `src/data/articles.js` 中添加新的文章数据：

```javascript
{
  id: 6,
  title: '新文章标题',
  excerpt: '文章摘要',
  content: '文章内容（HTML格式）',
  image: '文章封面图片URL',
  category: '分类名称',
  tags: ['标签1', '标签2'],
  author: '作者名称',
  createdAt: '2024-01-01',
  views: 0,
  likes: 0,
  comments: 0
}
```

### 自定义样式

主要样式文件位于 `src/assets/css/main.css`，包含：

- 全局样式重置
- 工具类样式
- 响应式断点
- 动画效果

### API接口

项目使用模拟API，位于 `src/api/blog.js`，包含：

- 文章相关接口
- 分类和标签接口
- 搜索接口
- 评论接口

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Vue Router](https://router.vuejs.org/) - Vue.js官方路由

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: <EMAIL>
- GitHub: [您的GitHub用户名](https://github.com/yourusername)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
