<template>
  <div class="home">
    <div class="container">
      <!-- 英雄区域 -->
      <section class="hero">
        <div class="hero-content">
          <h1 class="hero-title">欢迎来到我的博客</h1>
          <p class="hero-subtitle">分享技术心得，记录成长足迹</p>
          <router-link to="/articles" class="hero-btn">
            开始阅读
          </router-link>
        </div>
      </section>

      <div class="content-wrapper">
        <main class="main-content">
          <!-- 最新文章 -->
          <section class="section">
            <div class="section-header">
              <h2 class="section-title">最新文章</h2>
              <router-link to="/articles" class="section-more">查看更多</router-link>
            </div>
            <div class="articles-grid">
              <article 
                v-for="article in latestArticles" 
                :key="article.id" 
                class="article-card"
              >
                <div class="article-image">
                  <img :src="article.image" :alt="article.title" />
                  <div class="article-category">{{ article.category }}</div>
                </div>
                <div class="article-content">
                  <h3 class="article-title">
                    <router-link :to="`/article/${article.id}`">
                      {{ article.title }}
                    </router-link>
                  </h3>
                  <p class="article-excerpt">{{ article.excerpt }}</p>
                  <div class="article-meta">
                    <span class="article-date">{{ formatDate(article.createdAt) }}</span>
                    <span class="article-views">{{ article.views }} 阅读</span>
                  </div>
                </div>
              </article>
            </div>
          </section>

          <!-- 热门文章 -->
          <section class="section">
            <div class="section-header">
              <h2 class="section-title">热门文章</h2>
            </div>
            <div class="popular-articles">
              <article 
                v-for="(article, index) in popularArticles" 
                :key="article.id" 
                class="popular-article"
              >
                <div class="popular-rank">{{ index + 1 }}</div>
                <div class="popular-content">
                  <h4 class="popular-title">
                    <router-link :to="`/article/${article.id}`">
                      {{ article.title }}
                    </router-link>
                  </h4>
                  <div class="popular-meta">
                    <span class="popular-views">{{ article.views }} 阅读</span>
                    <span class="popular-date">{{ formatDate(article.createdAt) }}</span>
                  </div>
                </div>
              </article>
            </div>
          </section>
        </main>

        <aside class="sidebar-wrapper">
          <Sidebar />
        </aside>
      </div>
    </div>
  </div>
</template>

<script>
import Sidebar from '../components/Sidebar.vue'

export default {
  name: 'Home',
  components: {
    Sidebar
  },
  data() {
    return {
      latestArticles: [
        {
          id: 1,
          title: 'Vue 3 Composition API 深入解析',
          excerpt: '深入了解Vue 3的Composition API，掌握现代Vue开发的核心概念和最佳实践。',
          image: '/api/placeholder/300/200',
          category: 'Vue.js',
          createdAt: '2024-01-15',
          views: 1250
        },
        {
          id: 2,
          title: 'JavaScript ES2024 新特性详解',
          excerpt: '探索JavaScript最新版本的新特性，包括新的语法糖和API改进。',
          image: '/api/placeholder/300/200',
          category: 'JavaScript',
          createdAt: '2024-01-10',
          views: 980
        },
        {
          id: 3,
          title: 'CSS Grid 布局完全指南',
          excerpt: '从基础到高级，全面掌握CSS Grid布局系统，构建复杂的网页布局。',
          image: '/api/placeholder/300/200',
          category: 'CSS',
          createdAt: '2024-01-05',
          views: 756
        }
      ],
      popularArticles: [
        {
          id: 4,
          title: 'React vs Vue：2024年前端框架对比',
          createdAt: '2023-12-20',
          views: 2340
        },
        {
          id: 5,
          title: 'TypeScript 实战技巧分享',
          createdAt: '2023-12-15',
          views: 1890
        },
        {
          id: 6,
          title: 'Webpack 5 配置优化指南',
          createdAt: '2023-12-10',
          views: 1567
        },
        {
          id: 7,
          title: '前端性能优化最佳实践',
          createdAt: '2023-12-05',
          views: 1423
        },
        {
          id: 8,
          title: 'Node.js 微服务架构设计',
          createdAt: '2023-12-01',
          views: 1298
        }
      ]
    }
  },
  methods: {
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  margin-bottom: 40px;
  border-radius: 12px;
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-btn {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.8rem;
  color: #2c3e50;
  margin: 0;
}

.section-more {
  color: #3498db;
  text-decoration: none;
  font-weight: 500;
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.article-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.article-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-category {
  position: absolute;
  top: 15px;
  left: 15px;
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
}

.article-content {
  padding: 20px;
}

.article-title a {
  color: #2c3e50;
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.article-title a:hover {
  color: #3498db;
}

.article-excerpt {
  color: #666;
  line-height: 1.6;
  margin: 10px 0 15px;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #999;
}

.popular-articles {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.popular-article {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.popular-article:last-child {
  border-bottom: none;
}

.popular-rank {
  width: 30px;
  height: 30px;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.popular-content {
  flex: 1;
}

.popular-title a {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 500;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.popular-title a:hover {
  color: #3498db;
}

.popular-meta {
  margin-top: 5px;
  font-size: 0.85rem;
  color: #999;
}

.popular-meta span {
  margin-right: 15px;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .articles-grid {
    grid-template-columns: 1fr;
  }
}
</style>
