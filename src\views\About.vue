<template>
  <div class="about">
    <div class="container">
      <div class="content-wrapper">
        <main class="main-content">
          <div class="about-content">
            <header class="about-header">
              <div class="avatar">
                <img src="/api/placeholder/150/150" alt="头像" />
              </div>
              <h1 class="about-title">关于我</h1>
              <p class="about-subtitle">热爱技术的前端开发者</p>
            </header>

            <section class="about-section">
              <h2>个人简介</h2>
              <p>
                你好！我是一名热爱技术的前端开发者，专注于现代Web开发技术。
                我对Vue.js、React、JavaScript等前端技术有着浓厚的兴趣，
                喜欢探索新技术并将其应用到实际项目中。
              </p>
              <p>
                这个博客是我分享技术心得、记录学习过程的地方。
                希望通过我的文章能够帮助到更多的开发者，
                也欢迎大家与我交流讨论技术问题。
              </p>
            </section>

            <section class="about-section">
              <h2>技能专长</h2>
              <div class="skills">
                <div class="skill-category">
                  <h3>前端技术</h3>
                  <div class="skill-tags">
                    <span class="skill-tag">Vue.js</span>
                    <span class="skill-tag">React</span>
                    <span class="skill-tag">JavaScript</span>
                    <span class="skill-tag">TypeScript</span>
                    <span class="skill-tag">HTML5</span>
                    <span class="skill-tag">CSS3</span>
                  </div>
                </div>
                <div class="skill-category">
                  <h3>构建工具</h3>
                  <div class="skill-tags">
                    <span class="skill-tag">Vite</span>
                    <span class="skill-tag">Webpack</span>
                    <span class="skill-tag">Rollup</span>
                    <span class="skill-tag">ESBuild</span>
                  </div>
                </div>
                <div class="skill-category">
                  <h3>其他技术</h3>
                  <div class="skill-tags">
                    <span class="skill-tag">Node.js</span>
                    <span class="skill-tag">Git</span>
                    <span class="skill-tag">Docker</span>
                    <span class="skill-tag">Linux</span>
                  </div>
                </div>
              </div>
            </section>

            <section class="about-section">
              <h2>联系方式</h2>
              <div class="contact-info">
                <div class="contact-item">
                  <div class="contact-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </div>
                  <span><EMAIL></span>
                </div>
                <div class="contact-item">
                  <div class="contact-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M9 19c-5 0-8-3-8-8s3-8 8-8 8 3 8 8-3 8-8 8z"></path>
                      <path d="M8.5 14.5L12 11l3.5 3.5L12 18l-3.5-3.5z"></path>
                    </svg>
                  </div>
                  <a href="https://github.com/yourusername" target="_blank">GitHub</a>
                </div>
                <div class="contact-item">
                  <div class="contact-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                      <rect x="2" y="9" width="4" height="12"></rect>
                      <circle cx="4" cy="4" r="2"></circle>
                    </svg>
                  </div>
                  <a href="https://linkedin.com/in/yourusername" target="_blank">LinkedIn</a>
                </div>
              </div>
            </section>

            <section class="about-section">
              <h2>博客统计</h2>
              <div class="stats">
                <div class="stat-item">
                  <div class="stat-number">{{ stats.articles }}</div>
                  <div class="stat-label">文章数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.views }}</div>
                  <div class="stat-label">总阅读量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.comments }}</div>
                  <div class="stat-label">评论数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ stats.tags }}</div>
                  <div class="stat-label">标签数量</div>
                </div>
              </div>
            </section>
          </div>
        </main>

        <aside class="sidebar-wrapper">
          <Sidebar />
        </aside>
      </div>
    </div>
  </div>
</template>

<script>
import Sidebar from '../components/Sidebar.vue'

export default {
  name: 'About',
  components: {
    Sidebar
  },
  data() {
    return {
      stats: {
        articles: 25,
        views: 15680,
        comments: 128,
        tags: 18
      }
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.about-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.about-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  text-align: center;
}

.avatar {
  margin-bottom: 20px;
}

.avatar img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

.about-title {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.about-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

.about-section {
  padding: 30px 40px;
  border-bottom: 1px solid #eee;
}

.about-section:last-child {
  border-bottom: none;
}

.about-section h2 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.5rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.about-section p {
  color: #666;
  line-height: 1.8;
  margin-bottom: 15px;
}

.skills {
  display: grid;
  gap: 20px;
}

.skill-category h3 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  background: #f8f9fa;
  color: #666;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.contact-info {
  display: grid;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
}

.contact-icon {
  color: #3498db;
  flex-shrink: 0;
}

.contact-item a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-item a:hover {
  color: #2980b9;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #3498db;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .about-header {
    padding: 30px 20px;
  }
  
  .about-title {
    font-size: 2rem;
  }
  
  .about-section {
    padding: 20px;
  }
  
  .stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
