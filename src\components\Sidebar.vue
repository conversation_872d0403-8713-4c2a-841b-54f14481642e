<template>
  <aside class="sidebar">
    <!-- 个人简介 -->
    <div class="widget">
      <h3 class="widget-title">关于我</h3>
      <div class="profile">
        <div class="avatar">
          <img src="/api/placeholder/80/80" alt="头像" />
        </div>
        <p class="bio">
          热爱技术的前端开发者，专注于Vue.js、JavaScript等前端技术的学习和分享。
        </p>
      </div>
    </div>

    <!-- 最新文章 -->
    <div class="widget">
      <h3 class="widget-title">最新文章</h3>
      <ul class="recent-posts">
        <li v-for="article in recentArticles" :key="article.id" class="recent-post">
          <router-link :to="`/article/${article.id}`" class="recent-post-link">
            <div class="recent-post-meta">
              <span class="recent-post-date">{{ formatDate(article.createdAt) }}</span>
            </div>
            <h4 class="recent-post-title">{{ article.title }}</h4>
          </router-link>
        </li>
      </ul>
    </div>

    <!-- 分类 -->
    <div class="widget">
      <h3 class="widget-title">分类</h3>
      <ul class="categories">
        <li v-for="category in categories" :key="category.name" class="category-item">
          <router-link :to="`/category/${category.name}`" class="category-link">
            <span class="category-name">{{ category.name }}</span>
            <span class="category-count">({{ category.count }})</span>
          </router-link>
        </li>
      </ul>
    </div>

    <!-- 标签云 -->
    <div class="widget">
      <h3 class="widget-title">标签</h3>
      <div class="tag-cloud">
        <router-link 
          v-for="tag in tags" 
          :key="tag.name"
          :to="`/tag/${tag.name}`"
          class="tag"
          :style="{ fontSize: getTagSize(tag.count) + 'px' }"
        >
          {{ tag.name }}
        </router-link>
      </div>
    </div>

    <!-- 归档 -->
    <div class="widget">
      <h3 class="widget-title">归档</h3>
      <ul class="archives">
        <li v-for="archive in archives" :key="archive.month" class="archive-item">
          <router-link :to="`/articles?month=${archive.month}`" class="archive-link">
            <span class="archive-month">{{ archive.month }}</span>
            <span class="archive-count">({{ archive.count }})</span>
          </router-link>
        </li>
      </ul>
    </div>
  </aside>
</template>

<script>
export default {
  name: 'Sidebar',
  data() {
    return {
      recentArticles: [
        { id: 1, title: 'Vue 3 Composition API 深入解析', createdAt: '2024-01-15' },
        { id: 2, title: 'JavaScript ES2024 新特性', createdAt: '2024-01-10' },
        { id: 3, title: 'CSS Grid 布局完全指南', createdAt: '2024-01-05' },
        { id: 4, title: 'Vite 构建工具使用技巧', createdAt: '2024-01-01' }
      ],
      categories: [
        { name: 'Vue.js', count: 12 },
        { name: 'JavaScript', count: 8 },
        { name: 'CSS', count: 6 },
        { name: '工具', count: 4 },
        { name: '生活', count: 3 }
      ],
      tags: [
        { name: 'Vue3', count: 10 },
        { name: 'JavaScript', count: 8 },
        { name: 'CSS', count: 6 },
        { name: 'Vite', count: 5 },
        { name: '前端', count: 12 },
        { name: '响应式', count: 4 },
        { name: '组件', count: 7 }
      ],
      archives: [
        { month: '2024年1月', count: 5 },
        { month: '2023年12月', count: 8 },
        { month: '2023年11月', count: 6 },
        { month: '2023年10月', count: 4 }
      ]
    }
  },
  methods: {
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    },
    getTagSize(count) {
      const minSize = 12
      const maxSize = 18
      const maxCount = Math.max(...this.tags.map(tag => tag.count))
      return minSize + (count / maxCount) * (maxSize - minSize)
    }
  }
}
</script>

<style scoped>
.sidebar {
  width: 100%;
}

.widget {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.widget-title {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.profile {
  text-align: center;
}

.avatar {
  margin-bottom: 15px;
}

.avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.bio {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.recent-posts {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recent-post {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.recent-post:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.recent-post-link {
  text-decoration: none;
  color: inherit;
}

.recent-post-meta {
  margin-bottom: 5px;
}

.recent-post-date {
  font-size: 0.85rem;
  color: #999;
}

.recent-post-title {
  margin: 0;
  font-size: 0.95rem;
  color: #333;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.recent-post-link:hover .recent-post-title {
  color: #3498db;
}

.categories,
.archives {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item,
.archive-item {
  margin-bottom: 8px;
}

.category-link,
.archive-link {
  text-decoration: none;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  transition: color 0.3s ease;
}

.category-link:hover,
.archive-link:hover {
  color: #3498db;
}

.category-count,
.archive-count {
  color: #999;
  font-size: 0.9rem;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  text-decoration: none;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 15px;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.tag:hover {
  background: #3498db;
  color: #fff;
  border-color: #3498db;
}

@media (max-width: 768px) {
  .widget {
    margin-bottom: 15px;
    padding: 15px;
  }
}
</style>
