<template>
  <div class="article-detail">
    <div class="container">
      <div class="content-wrapper" v-if="article">
        <main class="main-content">
          <!-- 文章头部 -->
          <header class="article-header">
            <div class="article-meta">
              <router-link :to="`/category/${article.category}`" class="article-category">
                {{ article.category }}
              </router-link>
              <span class="article-date">{{ formatDate(article.createdAt) }}</span>
              <span class="article-views">{{ article.views }} 阅读</span>
            </div>
            <h1 class="article-title">{{ article.title }}</h1>
            <div class="article-tags">
              <router-link 
                v-for="tag in article.tags" 
                :key="tag"
                :to="`/tag/${tag}`"
                class="tag"
              >
                {{ tag }}
              </router-link>
            </div>
          </header>

          <!-- 文章内容 -->
          <article class="article-content">
            <div class="article-image" v-if="article.image">
              <img :src="article.image" :alt="article.title" />
            </div>
            <div class="content" v-html="article.content"></div>
          </article>

          <!-- 文章导航 -->
          <nav class="article-nav">
            <router-link 
              v-if="prevArticle" 
              :to="`/article/${prevArticle.id}`" 
              class="nav-link prev"
            >
              <span class="nav-label">上一篇</span>
              <span class="nav-title">{{ prevArticle.title }}</span>
            </router-link>
            <router-link 
              v-if="nextArticle" 
              :to="`/article/${nextArticle.id}`" 
              class="nav-link next"
            >
              <span class="nav-label">下一篇</span>
              <span class="nav-title">{{ nextArticle.title }}</span>
            </router-link>
          </nav>

          <!-- 评论区 -->
          <section class="comments-section">
            <h3 class="comments-title">评论 ({{ comments.length }})</h3>
            
            <!-- 评论表单 -->
            <form @submit.prevent="submitComment" class="comment-form">
              <div class="form-group">
                <label for="name">姓名 *</label>
                <input 
                  type="text" 
                  id="name" 
                  v-model="commentForm.name" 
                  required 
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="email">邮箱 *</label>
                <input 
                  type="email" 
                  id="email" 
                  v-model="commentForm.email" 
                  required 
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label for="content">评论内容 *</label>
                <textarea 
                  id="content" 
                  v-model="commentForm.content" 
                  required 
                  rows="4"
                  class="form-textarea"
                  placeholder="请输入您的评论..."
                ></textarea>
              </div>
              <button type="submit" class="submit-btn" :disabled="isSubmitting">
                {{ isSubmitting ? '提交中...' : '发表评论' }}
              </button>
            </form>

            <!-- 评论列表 -->
            <div class="comments-list">
              <div v-for="comment in comments" :key="comment.id" class="comment">
                <div class="comment-avatar">
                  <img :src="comment.avatar" :alt="comment.name" />
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-name">{{ comment.name }}</span>
                    <span class="comment-date">{{ formatDate(comment.createdAt) }}</span>
                  </div>
                  <p class="comment-text">{{ comment.content }}</p>
                </div>
              </div>
            </div>
          </section>
        </main>

        <aside class="sidebar-wrapper">
          <Sidebar />
        </aside>
      </div>

      <!-- 加载状态 -->
      <div v-else class="loading">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
    </div>
  </div>
</template>

<script>
import Sidebar from '../components/Sidebar.vue'

export default {
  name: 'ArticleDetail',
  components: {
    Sidebar
  },
  data() {
    return {
      article: null,
      prevArticle: null,
      nextArticle: null,
      comments: [
        {
          id: 1,
          name: '张三',
          avatar: '/api/placeholder/40/40',
          content: '这篇文章写得很好，学到了很多东西！',
          createdAt: '2024-01-16'
        },
        {
          id: 2,
          name: '李四',
          avatar: '/api/placeholder/40/40',
          content: '感谢分享，对我的项目很有帮助。',
          createdAt: '2024-01-17'
        }
      ],
      commentForm: {
        name: '',
        email: '',
        content: ''
      },
      isSubmitting: false
    }
  },
  created() {
    this.loadArticle()
  },
  watch: {
    '$route.params.id'() {
      this.loadArticle()
    }
  },
  methods: {
    loadArticle() {
      const id = parseInt(this.$route.params.id)
      
      // 模拟文章数据
      const articles = [
        {
          id: 1,
          title: 'Vue 3 Composition API 深入解析',
          content: `
            <p>Vue 3 引入了 Composition API，这是一个全新的 API 设计，旨在解决 Vue 2 中一些复杂组件的逻辑复用问题。</p>
            
            <h2>什么是 Composition API？</h2>
            <p>Composition API 是一套基于函数的 API，允许我们更灵活地组织组件逻辑。与 Options API 不同，Composition API 将相关的逻辑组织在一起，而不是按照选项类型分离。</p>
            
            <h2>基本用法</h2>
            <pre><code>import { ref, reactive, computed, onMounted } from 'vue'

export default {
  setup() {
    const count = ref(0)
    const state = reactive({
      name: 'Vue 3',
      version: '3.0'
    })
    
    const doubleCount = computed(() => count.value * 2)
    
    const increment = () => {
      count.value++
    }
    
    onMounted(() => {
      console.log('组件已挂载')
    })
    
    return {
      count,
      state,
      doubleCount,
      increment
    }
  }
}</code></pre>
            
            <h2>优势</h2>
            <ul>
              <li>更好的逻辑复用</li>
              <li>更好的类型推导</li>
              <li>更小的生产包体积</li>
              <li>更好的 Tree-shaking 支持</li>
            </ul>
            
            <p>Composition API 为 Vue 3 带来了更强大的功能和更好的开发体验，是现代 Vue 开发的重要组成部分。</p>
          `,
          image: '/api/placeholder/800/400',
          category: 'Vue.js',
          tags: ['Vue3', 'Composition API', '前端'],
          createdAt: '2024-01-15',
          views: 1250
        }
      ]
      
      this.article = articles.find(a => a.id === id)
      
      if (this.article) {
        // 模拟上一篇和下一篇文章
        this.prevArticle = id > 1 ? { id: id - 1, title: '上一篇文章标题' } : null
        this.nextArticle = id < 10 ? { id: id + 1, title: '下一篇文章标题' } : null
        
        // 增加阅读量
        this.article.views++
      }
    },
    submitComment() {
      if (this.isSubmitting) return
      
      this.isSubmitting = true
      
      // 模拟提交评论
      setTimeout(() => {
        const newComment = {
          id: this.comments.length + 1,
          name: this.commentForm.name,
          avatar: '/api/placeholder/40/40',
          content: this.commentForm.content,
          createdAt: new Date().toISOString().split('T')[0]
        }
        
        this.comments.unshift(newComment)
        
        // 重置表单
        this.commentForm = {
          name: '',
          email: '',
          content: ''
        }
        
        this.isSubmitting = false
        
        alert('评论发表成功！')
      }, 1000)
    },
    formatDate(dateString) {
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
}

.article-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.article-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.article-category {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  text-decoration: none;
  font-weight: 500;
}

.article-date,
.article-views {
  color: #666;
}

.article-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 20px 0;
  line-height: 1.3;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background: #f8f9fa;
  color: #666;
  padding: 4px 12px;
  border-radius: 15px;
  text-decoration: none;
  font-size: 0.85rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.tag:hover {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.article-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.article-image {
  margin-bottom: 30px;
  text-align: center;
}

.article-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.content {
  line-height: 1.8;
  color: #333;
}

.content h2 {
  color: #2c3e50;
  margin: 30px 0 15px 0;
  font-size: 1.5rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.content p {
  margin-bottom: 15px;
}

.content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.content li {
  margin-bottom: 8px;
}

.content pre {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
  border-left: 4px solid #3498db;
}

.content code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.article-nav {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.nav-link {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.nav-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-link.prev {
  text-align: left;
}

.nav-link.next {
  text-align: right;
}

.nav-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
}

.nav-title {
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.4;
}

.comments-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.comments-title {
  color: #2c3e50;
  margin-bottom: 25px;
  font-size: 1.5rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.comment-form {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3498db;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.submit-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #2980b9;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.comments-list {
  margin-top: 20px;
}

.comment {
  display: flex;
  gap: 15px;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
}

.comment:last-child {
  border-bottom: none;
}

.comment-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
  align-items: center;
}

.comment-name {
  font-weight: 500;
  color: #2c3e50;
}

.comment-date {
  font-size: 0.85rem;
  color: #999;
}

.comment-text {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.loading {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .article-title {
    font-size: 2rem;
  }

  .article-nav {
    grid-template-columns: 1fr;
  }

  .article-header,
  .article-content,
  .comments-section {
    padding: 20px;
  }
}
</style>
